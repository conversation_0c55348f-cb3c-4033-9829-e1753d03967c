# 月度报告提示词优化说明 - 图表合并策略

## 优化目标

基于 `output_月度报告_s2.json` 的分析结果，对 `prom_月度报告_s2_md2json.md` 提示词进行优化，主要解决图表过度拆分的问题，提高数据展示的关联性和可读性。

## 问题分析

### 当前输出结果中的图表拆分问题

1. **序列号 "2.1.1" 和 "2.1.2"**：
   - 2.1.1: 月度成交套数(BAR图) - 独立图表
   - 2.1.2: 月度成交环比变化(LINE图) - 独立图表
   - **问题**：这两个数据高度相关，拆分展示降低了数据关联性

2. **序列号 "2.2.1" 和 "2.2.2"**：
   - 2.2.1: 价格走势对比(BAR图) - 独立图表
   - 2.2.2: 价格变化指标(BAR图) - 独立图表
   - **问题**：都是价格相关数据，应该合并展示

3. **序列号 "3.3.1" 和 "3.3.2"**：
   - 3.3.1: 土地楼板价对比(BAR图) - 独立图表
   - 3.3.2: 土地成交建面对比(BAR图) - 独立图表
   - **问题**：都是土地市场数据，可以合并为MIXED图表

## 优化方案

### 1. 新增图表合并优先原则

在提示词中添加了**图表合并优先原则**：

- **优先合并相关数据**：相关性强的数据应尽量合并在同一图表中展示
- **MIXED图表应用**：当数据包含不同类型但相关的指标时，优先使用MIXED样式合并展示
  - 数量、价格、面积等绝对值数据使用柱状图（BAR）
  - 变化率、增长率、占比变化等相对值数据使用折线图（LINE）

### 2. 明确数据关联性判断标准

- 同一业务指标的不同维度（如成交套数与环比变化）→ 合并为MIXED图表
- 同一时间段的相关指标（如价格走势与变化指标）→ 合并为MIXED图表
- 同一业务领域的不同数据类型（如土地楼板价与成交建面）→ 合并为MIXED图表

### 3. 添加MIXED图表示例

在提示词中添加了具体的MIXED图表JSON示例：

```json
{
  "serial": "2.1.1",
  "type": "CHART",
  "style": "MIXED",
  "title": "月度成交套数及环比变化",
  "cols": ["2025/01", "2025/02", "2025/06"],
  "content": [
    {
      "title": "成交套数(套)",
      "chartType": "BAR",
      "content": [261, 468, 206]
    },
    {
      "title": "环比变化(%)",
      "chartType": "LINE", 
      "content": [-37.6, 79.3, -53.8]
    }
  ]
}
```

### 4. 具体合并指导

针对当前输出结果中的具体问题，添加了明确的合并指导：

- **成交量分析**：将"月度成交套数"和"环比变化"合并为一个MIXED图表
- **价格走势分析**：将"价格走势对比"和"价格变化指标"合并为一个MIXED图表
- **土地市场分析**：将"楼板价对比"和"成交建面对比"合并为一个MIXED图表
- **小区对比分析**：将"均价对比"和"成交套数对比"合并为一个MIXED图表

### 5. 更新检查清单

修改了图表化执行检查清单，强调合并展示：

- **成交量数据**：月度成交套数(BAR) + 环比变化(LINE) → 合并为单个MIXED图表
- **价格数据**：年初价格vs当前价格(BAR) + 累计降幅/溢价数据(LINE) → 合并为单个MIXED图表
- **土地市场**：楼板价对比(BAR) + 成交建面对比(LINE) → 合并为单个MIXED图表

### 6. 调整预期结果

将预期的CHART控件数量从"10个以上"调整为"6-8个高质量合并图表"，强调质量优于数量。

## 预期效果

通过这些优化，预期能够实现：

1. **减少冗余图表**：将相关的独立图表合并，减少图表总数
2. **提高数据关联性**：相关数据在同一图表中展示，便于对比分析
3. **优化用户体验**：减少用户在多个图表间切换的需要
4. **保持数据完整性**：合并后的图表仍然包含所有原始数据信息

## 技术实现

- 使用MIXED样式的CHART控件
- 通过chartType字段区分BAR和LINE展示方式
- 保持原有的数据结构和单位处理规则
- 维持序列编号的连续性和层级关系

## 质量保证

在质量检查清单中新增了"图表合并优化"检查项，确保：
- 相关性强的数据已合并为MIXED图表
- 避免过度拆分
- 图表数量控制在合理范围内
- 保持数据展示的逻辑性和可读性
